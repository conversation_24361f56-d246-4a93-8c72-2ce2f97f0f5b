{"name": "salon-copilot", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~51.0.28", "expo-status-bar": "~1.12.1", "react": "18.2.0", "react-native": "0.74.5", "react-native-web": "~0.19.10", "@expo/webpack-config": "^19.0.1"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}