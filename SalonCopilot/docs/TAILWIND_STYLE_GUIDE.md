# Guía de Estilos Tailwind CSS - Salon Copilot

Esta guía establece las convenciones y patrones de diseño para el uso de Tailwind CSS en la aplicación Salon Copilot.

## Paleta de Colores

### Colores Principales
- **Primary**: `primary-50` a `primary-900` - Rosa elegante para elementos principales
- **Secondary**: `secondary-50` a `secondary-900` - Dorado/champagne para acentos
- **Accent**: `accent-50` a `accent-900` - Verde menta para destacar

### Colores del Sistema
- **Success**: `green-500` - Para estados exitosos
- **Warning**: `yellow-500` - Para advertencias
- **Error**: `red-500` - Para errores
- **Info**: `blue-500` - Para información

### Colores Neutros
- **Neutral**: `neutral-50` a `neutral-900` - Grises para texto y fondos

## Tipografía

### Jerarquía de Texto
```javascript
// Títulos principales
'text-3xl font-bold text-neutral-900'

// Subtítulos
'text-xl font-semibold text-neutral-800'

// Texto del cuerpo
'text-base text-neutral-700'

// Texto secundario
'text-sm text-neutral-600'

// Texto pequeño/metadatos
'text-xs text-neutral-500'
```

### Familias de Fuente
- **Sans**: Fuente principal para UI
- **Serif**: Para títulos elegantes (opcional)
- **Mono**: Para código o datos técnicos

## Espaciado y Layout

### Sistema de Espaciado
- **Micro**: `p-1`, `m-1` (4px)
- **Pequeño**: `p-2`, `m-2` (8px)
- **Mediano**: `p-4`, `m-4` (16px)
- **Grande**: `p-6`, `m-6` (24px)
- **Extra Grande**: `p-8`, `m-8` (32px)

### Contenedores
```javascript
// Contenedor principal
'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'

// Contenedor de contenido
'max-w-4xl mx-auto'

// Contenedor estrecho
'max-w-2xl mx-auto'
```

## Componentes Base

### Botones
```javascript
// Botón primario
'bg-primary-500 text-white px-4 py-3 rounded-xl font-semibold shadow-soft active:bg-primary-600'

// Botón secundario
'bg-secondary-500 text-white px-4 py-3 rounded-xl font-semibold shadow-soft active:bg-secondary-600'

// Botón outline
'border-2 border-primary-500 text-primary-500 px-4 py-3 rounded-xl font-semibold bg-transparent active:bg-primary-50'
```

### Cards
```javascript
// Card básica
'bg-white rounded-2xl p-4 shadow-soft border border-neutral-200'

// Card elevada
'bg-white rounded-2xl p-4 shadow-soft'

// Card con borde
'bg-white rounded-2xl p-4 border-2 border-primary-200'
```

### Inputs
```javascript
// Input básico
'w-full px-4 py-3 border border-neutral-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500'

// Input con error
'w-full px-4 py-3 border border-red-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500'
```

## Sombras Personalizadas

### Definiciones en tailwind.config.js
```javascript
boxShadow: {
  'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
  'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  'strong': '0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
}
```

### Uso
- **Soft**: Para cards y elementos sutiles
- **Medium**: Para modales y elementos flotantes
- **Strong**: Para elementos que necesitan destacar mucho

## Responsive Design

### Breakpoints
- **sm**: 640px - Teléfonos grandes
- **md**: 768px - Tablets
- **lg**: 1024px - Laptops
- **xl**: 1280px - Desktops
- **2xl**: 1536px - Pantallas grandes

### Patrones Responsive
```javascript
// Texto responsive
'text-sm sm:text-base lg:text-lg'

// Padding responsive
'p-4 sm:p-6 lg:p-8'

// Grid responsive
'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
```

## Estados Interactivos

### Hover States
```javascript
// Botones
'hover:bg-primary-600 transition-colors duration-200'

// Cards
'hover:shadow-medium transition-shadow duration-200'
```

### Focus States
```javascript
// Elementos focusables
'focus:ring-2 focus:ring-primary-500 focus:ring-offset-2'
```

### Active States
```javascript
// Botones
'active:bg-primary-700 active:scale-95 transition-transform duration-100'
```

## Animaciones y Transiciones

### Transiciones Básicas
```javascript
// Transición suave
'transition-all duration-200 ease-in-out'

// Transición de color
'transition-colors duration-200'

// Transición de sombra
'transition-shadow duration-200'
```

### Animaciones Personalizadas
```javascript
// Fade in
'animate-fade-in'

// Slide up
'animate-slide-up'

// Bounce
'animate-bounce-soft'
```

## Patrones de Layout

### Flexbox
```javascript
// Centrado
'flex items-center justify-center'

// Espacio entre elementos
'flex items-center justify-between'

// Columna centrada
'flex flex-col items-center'
```

### Grid
```javascript
// Grid básico
'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'

// Grid de cards
'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
```

## Mejores Prácticas

### Organización de Clases
1. **Layout**: `flex`, `grid`, `block`
2. **Posicionamiento**: `relative`, `absolute`, `top-0`
3. **Dimensiones**: `w-full`, `h-screen`
4. **Espaciado**: `p-4`, `m-2`
5. **Tipografía**: `text-lg`, `font-bold`
6. **Colores**: `bg-white`, `text-gray-900`
7. **Bordes**: `border`, `rounded-lg`
8. **Efectos**: `shadow-lg`, `opacity-50`
9. **Interactividad**: `hover:`, `focus:`

### Convenciones de Nomenclatura
- Usar nombres descriptivos para componentes
- Agrupar clases relacionadas
- Usar comentarios para secciones complejas

### Performance
- Purgar clases no utilizadas en producción
- Usar clases utilitarias en lugar de CSS personalizado
- Minimizar el uso de `@apply` en favor de clases utilitarias

## Componentes Específicos de Salon Copilot

### Appointment Card
```javascript
'bg-white rounded-2xl p-4 shadow-soft border-l-4 border-primary-500'
```

### Service Badge
```javascript
'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-accent-100 text-accent-800'
```

### Status Indicator
```javascript
// Activo
'w-3 h-3 bg-green-500 rounded-full'

// Pendiente
'w-3 h-3 bg-yellow-500 rounded-full'

// Inactivo
'w-3 h-3 bg-gray-400 rounded-full'
```

## Accesibilidad

### Contraste de Colores
- Asegurar ratio mínimo de 4.5:1 para texto normal
- Ratio mínimo de 3:1 para texto grande
- Usar herramientas de verificación de contraste

### Estados de Focus
- Siempre incluir estados de focus visibles
- Usar `focus:ring-2` para elementos interactivos
- Mantener consistencia en los estilos de focus

### Texto Alternativo
- Usar clases de screen reader cuando sea necesario
- `sr-only` para texto solo para lectores de pantalla