// Mock WebSocket implementation for React Native
// This is used to replace the 'ws' module which is not compatible with React Native

class MockWebSocket {
  constructor(url, protocols, options) {
    this.url = url;
    this.protocols = protocols;
    this.options = options;
    this.readyState = 0; // CONNECTING
    this.CONNECTING = 0;
    this.OPEN = 1;
    this.CLOSING = 2;
    this.CLOSED = 3;
    
    // Use React Native's WebSocket if available
    if (typeof WebSocket !== 'undefined') {
      return new WebSocket(url, protocols);
    }
    
    // Fallback for environments without WebSocket
    setTimeout(() => {
      this.readyState = 3; // CLOSED
      if (this.onerror) {
        this.onerror(new Error('WebSocket not available in this environment'));
      }
    }, 0);
  }

  send(data) {
    if (this.readyState !== 1) {
      throw new Error('WebSocket is not open');
    }
  }

  close(code, reason) {
    this.readyState = 3; // CLOSED
    if (this.onclose) {
      this.onclose({ code, reason });
    }
  }

  addEventListener(type, listener) {
    if (type === 'open') this.onopen = listener;
    if (type === 'message') this.onmessage = listener;
    if (type === 'error') this.onerror = listener;
    if (type === 'close') this.onclose = listener;
  }

  removeEventListener(type, listener) {
    if (type === 'open' && this.onopen === listener) this.onopen = null;
    if (type === 'message' && this.onmessage === listener) this.onmessage = null;
    if (type === 'error' && this.onerror === listener) this.onerror = null;
    if (type === 'close' && this.onclose === listener) this.onclose = null;
  }
}

// Export the mock WebSocket
module.exports = MockWebSocket;
module.exports.default = MockWebSocket;
module.exports.WebSocket = MockWebSocket;

// Constants
module.exports.CONNECTING = 0;
module.exports.OPEN = 1;
module.exports.CLOSING = 2;
module.exports.CLOSED = 3;
