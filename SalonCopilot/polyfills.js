// Polyfills for React Native
import 'react-native-get-random-values';
import 'react-native-url-polyfill/auto';

// Import polyfills
import { Buffer } from 'buffer';

// Basic global polyfills
if (typeof global !== 'undefined') {
  global.process = global.process || { env: {} };
  global.Buffer = Buffer;
}

// For web environments
if (typeof window !== 'undefined') {
  window.process = window.process || { env: {} };
  window.Buffer = Buffer;
}
