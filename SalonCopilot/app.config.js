export default {
  expo: {
    name: "Salon Copilot",
    slug: "salon-copilot",
    version: "1.0.0",
    orientation: "portrait",
    sdkVersion: "53.0.0",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    splash: {
      image: "./assets/splash.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#ffffff"
      }
    },
    web: {
      favicon: "./assets/favicon.png"
    },
    extra: {
      supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL,
      supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
      environment: process.env.EXPO_PUBLIC_ENVIRONMENT || 'development'
    },
    plugins: [
      "expo-secure-store"
    ]
  }
};