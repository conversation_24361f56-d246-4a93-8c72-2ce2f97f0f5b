import React from 'react';
import { View } from 'react-native';

const Card = ({ 
  children, 
  variant = 'default',
  padding = 'md',
  className = '',
  ...props 
}) => {
  // Variantes de estilo
  const variants = {
    default: 'bg-white border border-neutral-200',
    elevated: 'bg-white shadow-soft',
    outlined: 'bg-white border-2 border-primary-200',
    filled: 'bg-primary-50 border border-primary-100',
    glass: 'bg-white/80 backdrop-blur-sm border border-white/20',
  };

  // Tamaños de padding
  const paddings = {
    none: '',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8',
  };

  return (
    <View
      className={`
        ${variants[variant]}
        ${paddings[padding]}
        rounded-2xl
        ${className}
      `}
      {...props}
    >
      {children}
    </View>
  );
};

// Componente para el header de la card
const CardHeader = ({ children, className = '', ...props }) => (
  <View className={`mb-4 ${className}`} {...props}>
    {children}
  </View>
);

// Componente para el contenido de la card
const CardContent = ({ children, className = '', ...props }) => (
  <View className={`${className}`} {...props}>
    {children}
  </View>
);

// Componente para el footer de la card
const CardFooter = ({ children, className = '', ...props }) => (
  <View className={`mt-4 pt-4 border-t border-neutral-100 ${className}`} {...props}>
    {children}
  </View>
);

// Exportar componentes
Card.Header = CardHeader;
Card.Content = CardContent;
Card.Footer = CardFooter;

export default Card;