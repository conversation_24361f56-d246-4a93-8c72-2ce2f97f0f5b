import React from 'react';
import { TouchableOpacity, Text } from 'react-native';

const Button = ({ 
  children, 
  onPress, 
  variant = 'primary', 
  size = 'md', 
  disabled = false,
  className = '',
  ...props 
}) => {
  // Variantes de estilo
  const variants = {
    primary: 'bg-primary-500 active:bg-primary-600',
    secondary: 'bg-secondary-500 active:bg-secondary-600',
    accent: 'bg-accent-500 active:bg-accent-600',
    outline: 'border-2 border-primary-500 bg-transparent active:bg-primary-50',
    ghost: 'bg-transparent active:bg-neutral-100',
    danger: 'bg-error active:bg-red-600',
  };

  // Tamaños
  const sizes = {
    sm: 'px-3 py-2 rounded-lg',
    md: 'px-4 py-3 rounded-xl',
    lg: 'px-6 py-4 rounded-2xl',
  };

  // Estilos de texto
  const textVariants = {
    primary: 'text-white font-semibold',
    secondary: 'text-white font-semibold',
    accent: 'text-white font-semibold',
    outline: 'text-primary-500 font-semibold',
    ghost: 'text-neutral-700 font-medium',
    danger: 'text-white font-semibold',
  };

  const textSizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  // Estilos cuando está deshabilitado
  const disabledStyles = disabled ? 'opacity-50' : '';

  return (
    <TouchableOpacity
      onPress={disabled ? undefined : onPress}
      className={`
        ${variants[variant]} 
        ${sizes[size]} 
        ${disabledStyles}
        items-center justify-center
        shadow-soft
        ${className}
      `}
      disabled={disabled}
      {...props}
    >
      <Text className={`${textVariants[variant]} ${textSizes[size]}`}>
        {children}
      </Text>
    </TouchableOpacity>
  );
};

export default Button;