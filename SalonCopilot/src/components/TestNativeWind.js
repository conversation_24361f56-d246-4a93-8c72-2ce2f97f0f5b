import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

export default function TestNativeWind() {
  return (
    <View className="flex-1 justify-center items-center bg-gray-100 p-4">
      <View className="bg-white rounded-2xl p-6 shadow-lg w-full max-w-sm">
        <Text className="text-2xl font-bold text-gray-800 text-center mb-4">
          🎨 NativeWind Test
        </Text>
        <Text className="text-gray-600 text-center mb-6">
          Si ves este texto con estilos, ¡NativeWind está funcionando!
        </Text>
        <TouchableOpacity className="bg-primary-500 py-3 px-6 rounded-xl">
          <Text className="text-white font-semibold text-center">
            Botón de Prueba
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
