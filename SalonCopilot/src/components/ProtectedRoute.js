import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { useAuth } from '../contexts/AuthContext';

export default function ProtectedRoute({ children, requireOnboarding = false }) {
  const { user, profile, loading, initialized, isAuthenticated, hasCompletedOnboarding } = useAuth();

  // Show loading spinner while auth is initializing
  if (!initialized || loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ec4899" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  // If not authenticated, this will be handled by the navigation
  if (!isAuthenticated()) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Please sign in to continue</Text>
      </View>
    );
  }

  // If onboarding is required but not completed
  if (requireOnboarding && !hasCompletedOnboarding()) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Please complete your profile setup</Text>
      </View>
    );
  }

  // User is authenticated and meets requirements
  return children;
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fafafa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#525252',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fafafa',
    paddingHorizontal: 24,
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
  },
});
