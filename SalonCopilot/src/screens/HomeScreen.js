import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useAuth } from '../contexts/AuthContext';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fafafa', // neutral-50
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 32,
  },
  header: {
    marginBottom: 32,
  },
  title: {
    fontSize: 30,
    fontWeight: 'bold',
    color: '#171717', // neutral-900
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: '#525252', // neutral-600
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#262626', // neutral-800
    marginBottom: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 32,
  },
  primaryButton: {
    flex: 1,
    backgroundColor: '#ec4899', // primary-500
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: '#64748b', // secondary-500
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  buttonText: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 16,
  },
  card: {
    backgroundColor: '#ffffff',
    padding: 24,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.07,
    shadowRadius: 15,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#262626', // neutral-800
    marginBottom: 12,
  },
  cardValue: {
    fontSize: 30,
    fontWeight: 'bold',
    color: '#ec4899', // primary-500
    marginBottom: 8,
  },
  cardSubtext: {
    fontSize: 14,
    color: '#525252', // neutral-600
  },
  logoutButton: {
    backgroundColor: '#ef4444',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 16,
    alignSelf: 'center',
  },
  logoutButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default function HomeScreen() {
  const { user, profile, signOut } = useAuth();

  const handleLogout = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            const { error } = await signOut();
            if (error) {
              Alert.alert('Error', 'Failed to sign out');
            }
          },
        },
      ]
    );
  };

  const getWelcomeMessage = () => {
    const firstName = profile?.first_name || user?.email?.split('@')[0] || 'Professional';
    return `¡Bienvenido, ${firstName}!`;
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>
            {getWelcomeMessage()}
          </Text>
          <Text style={styles.subtitle}>
            Tu asistente para la gestión del salón
          </Text>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Sign Out</Text>
          </TouchableOpacity>
        </View>

        {/* Quick Actions */}
        <View style={{ marginBottom: 32 }}>
          <Text style={styles.sectionTitle}>
            Acciones Rápidas
          </Text>
          <View style={styles.buttonRow}>
            <TouchableOpacity style={styles.primaryButton}>
              <Text style={styles.buttonText}>Nueva Cita</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.secondaryButton}>
              <Text style={styles.buttonText}>Ver Agenda</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Dashboard Cards */}
        <View>
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Citas de Hoy</Text>
            <Text style={styles.cardValue}>12</Text>
            <Text style={styles.cardSubtext}>3 pendientes, 9 completadas</Text>
          </View>

          <View style={styles.card}>
            <Text style={styles.cardTitle}>Ingresos del Día</Text>
            <Text style={styles.cardValue}>€450</Text>
            <Text style={styles.cardSubtext}>+15% respecto a ayer</Text>
          </View>

          <View style={styles.card}>
            <Text style={styles.cardTitle}>Próximas Citas</Text>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
              <View>
                <Text style={{ fontSize: 16, fontWeight: '500', color: '#262626', marginBottom: 4 }}>María García</Text>
                <Text style={styles.cardSubtext}>Corte y peinado</Text>
              </View>
              <Text style={{ fontSize: 14, fontWeight: '500', color: '#ec4899' }}>14:30</Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <View>
                <Text style={{ fontSize: 16, fontWeight: '500', color: '#262626', marginBottom: 4 }}>Ana López</Text>
                <Text style={styles.cardSubtext}>Tinte y mechas</Text>
              </View>
              <Text style={{ fontSize: 14, fontWeight: '500', color: '#ec4899' }}>16:00</Text>
            </View>
          </View>
        </View>
      </View>
      <StatusBar style="auto" />
    </ScrollView>
  );
}