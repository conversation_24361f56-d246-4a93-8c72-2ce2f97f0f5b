// Database types for Salon Copilot
// Generated from Supabase schema

export type UserRole = 'professional' | 'admin' | 'client';
export type ProfessionalType = 'hairstylist' | 'colorist' | 'barber' | 'salon_owner' | 'freelancer';
export type AppointmentStatus = 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
export type InventoryMode = 'basic' | 'standard' | 'advanced';

export interface Profile {
  id: string;
  role: UserRole;
  professional_type?: ProfessionalType;
  salon_name?: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  email?: string;
  experience_level?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  preferred_brands?: string[];
  inventory_mode: InventoryMode;
  ai_learning_enabled: boolean;
  profile_image_url?: string;
  bio?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  social_links?: {
    instagram?: string;
    facebook?: string;
    website?: string;
  };
  settings: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface Client {
  id: string;
  professional_id: string;
  first_name: string;
  last_name: string;
  phone?: string;
  email?: string;
  date_of_birth?: string;
  hair_type?: string;
  hair_texture?: string;
  scalp_condition?: string;
  allergies?: string[];
  preferences: {
    preferred_time?: string;
    allergies?: string[];
    notes?: string;
  };
  notes?: string;
  profile_image_url?: string;
  emergency_contact?: {
    name?: string;
    phone?: string;
    relationship?: string;
  };
  created_at: string;
  updated_at: string;
}

export interface Service {
  id: string;
  professional_id: string;
  name: string;
  description?: string;
  duration_minutes: number;
  price?: number;
  category?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Availability {
  id: string;
  professional_id: string;
  day_of_week: number; // 0 = Sunday, 1 = Monday, etc.
  start_time: string;
  end_time: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface TimeBlock {
  id: string;
  professional_id: string;
  start_datetime: string;
  end_datetime: string;
  reason?: string;
  description?: string;
  is_recurring: boolean;
  recurrence_pattern?: {
    frequency?: 'daily' | 'weekly' | 'monthly';
    interval?: number;
    end_date?: string;
  };
  created_at: string;
  updated_at: string;
}

export interface Appointment {
  id: string;
  professional_id: string;
  client_id: string;
  service_id?: string;
  scheduled_date: string;
  duration_minutes: number;
  status: AppointmentStatus;
  notes?: string;
  internal_notes?: string;
  reminder_sent: boolean;
  confirmation_sent: boolean;
  price?: number;
  deposit_amount?: number;
  deposit_paid: boolean;
  created_at: string;
  updated_at: string;
  
  // Joined data
  clients?: Client;
  services?: Service;
}

export interface InventoryItem {
  id: string;
  professional_id: string;
  name: string;
  brand?: string;
  category?: string;
  subcategory?: string;
  sku?: string;
  barcode?: string;
  description?: string;
  current_stock: number;
  min_stock_level: number;
  max_stock_level?: number;
  unit_cost?: number;
  selling_price?: number;
  supplier_info?: {
    name?: string;
    contact?: string;
    website?: string;
  };
  expiry_date?: string;
  location?: string;
  is_active: boolean;
  image_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Formulation {
  id: string;
  professional_id: string;
  client_id: string;
  appointment_id?: string;
  name?: string;
  diagnosis?: {
    hair_condition?: string;
    porosity?: string;
    damage_level?: string;
    natural_color?: string;
    previous_treatments?: string[];
  };
  target_color?: {
    base_color?: string;
    tone?: string;
    level?: number;
    formula_type?: string;
  };
  formulation: {
    products: Array<{
      product_id?: string;
      product_name: string;
      quantity: number;
      unit: string;
      ratio?: number;
    }>;
    mixing_instructions?: string;
    processing_time?: number;
    application_method?: string;
  };
  result?: {
    achieved_color?: string;
    client_satisfaction?: number;
    notes?: string;
    before_images?: string[];
    after_images?: string[];
  };
  images?: string[];
  ai_accuracy_score?: number;
  professional_notes?: string;
  client_satisfaction?: number;
  is_template: boolean;
  created_at: string;
  updated_at: string;
  
  // Joined data
  clients?: Client;
  appointments?: Appointment;
}

// API Response types
export interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  status: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Form types
export interface CreateClientForm {
  first_name: string;
  last_name: string;
  phone?: string;
  email?: string;
  date_of_birth?: string;
  hair_type?: string;
  hair_texture?: string;
  scalp_condition?: string;
  allergies?: string[];
  notes?: string;
}

export interface CreateAppointmentForm {
  client_id: string;
  service_id?: string;
  scheduled_date: string;
  duration_minutes: number;
  notes?: string;
  price?: number;
}

export interface CreateServiceForm {
  name: string;
  description?: string;
  duration_minutes: number;
  price?: number;
  category?: string;
}

// Filter types
export interface AppointmentFilters {
  date?: string;
  status?: AppointmentStatus;
  client_id?: string;
  service_id?: string;
}

export interface ClientFilters {
  search?: string;
  hair_type?: string;
  hair_texture?: string;
}

export interface InventoryFilters {
  category?: string;
  brand?: string;
  low_stock?: boolean;
  search?: string;
}
