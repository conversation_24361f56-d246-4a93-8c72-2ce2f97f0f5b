import { supabase } from './supabase';

/**
 * Database service utilities for Salon Copilot
 * Provides common database operations and error handling
 */

// Generic error handler
const handleError = (error, operation) => {
  console.error(`Database error in ${operation}:`, error);
  throw new Error(`Failed to ${operation}: ${error.message}`);
};

// Test database connection
export const testConnection = async () => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error) throw error;
    
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
};

// Profile operations
export const profileService = {
  // Get current user profile
  async getCurrentProfile() {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      handleError(error, 'get current profile');
    }
  },

  // Create or update profile
  async upsertProfile(profileData) {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .upsert(profileData)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      handleError(error, 'upsert profile');
    }
  }
};

// Client operations
export const clientService = {
  // Get all clients for current user
  async getClients() {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .eq('professional_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      handleError(error, 'get clients');
    }
  },

  // Create new client
  async createClient(clientData) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      const { data, error } = await supabase
        .from('clients')
        .insert({ ...clientData, professional_id: user.id })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      handleError(error, 'create client');
    }
  }
};

// Appointment operations
export const appointmentService = {
  // Get appointments for current user
  async getAppointments(filters = {}) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      let query = supabase
        .from('appointments')
        .select(`
          *,
          clients (
            id,
            first_name,
            last_name,
            phone,
            email
          )
        `)
        .eq('professional_id', user.id);

      // Apply filters
      if (filters.date) {
        query = query.gte('scheduled_date', `${filters.date}T00:00:00`)
                    .lt('scheduled_date', `${filters.date}T23:59:59`);
      }
      
      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      query = query.order('scheduled_date', { ascending: true });

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      handleError(error, 'get appointments');
    }
  },

  // Create new appointment
  async createAppointment(appointmentData) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      const { data, error } = await supabase
        .from('appointments')
        .insert({ ...appointmentData, professional_id: user.id })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      handleError(error, 'create appointment');
    }
  }
};

// Real-time subscriptions
export const subscriptionService = {
  // Subscribe to appointments changes
  subscribeToAppointments(callback) {
    return supabase
      .channel('appointments')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'appointments' }, 
        callback
      )
      .subscribe();
  },

  // Subscribe to clients changes
  subscribeToClients(callback) {
    return supabase
      .channel('clients')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'clients' }, 
        callback
      )
      .subscribe();
  }
};

export default {
  testConnection,
  profileService,
  clientService,
  appointmentService,
  subscriptionService
};
