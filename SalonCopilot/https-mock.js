// Mock https implementation for React Native
// This is used to replace the 'https' module which is not compatible with React Native

const { EventEmitter } = require('events');

class MockIncomingMessage extends EventEmitter {
  constructor() {
    super();
    this.statusCode = 200;
    this.statusMessage = 'OK';
    this.headers = {};
    this.rawHeaders = [];
    this.httpVersion = '1.1';
    this.url = '';
    this.method = 'GET';
  }

  setTimeout(timeout, callback) {
    if (callback) {
      setTimeout(callback, timeout);
    }
  }
}

class MockClientRequest extends EventEmitter {
  constructor() {
    super();
    this.aborted = false;
    this.finished = false;
  }

  write(chunk, encoding, callback) {
    if (typeof encoding === 'function') {
      callback = encoding;
    }
    if (callback) callback();
    return true;
  }

  end(data, encoding, callback) {
    if (typeof data === 'function') {
      callback = data;
      data = null;
    } else if (typeof encoding === 'function') {
      callback = encoding;
      encoding = null;
    }
    
    if (data) {
      this.write(data, encoding);
    }
    
    this.finished = true;
    
    // Simulate async response
    setTimeout(() => {
      const response = new MockIncomingMessage();
      this.emit('response', response);
      
      // Simulate some data
      setTimeout(() => {
        response.emit('data', Buffer.from('{}'));
        response.emit('end');
      }, 10);
    }, 10);
    
    if (callback) callback();
  }

  abort() {
    this.aborted = true;
    this.emit('abort');
  }

  setTimeout(timeout, callback) {
    if (callback) {
      setTimeout(callback, timeout);
    }
  }

  setNoDelay(noDelay) {
    // Mock implementation
  }

  setSocketKeepAlive(enable, initialDelay) {
    // Mock implementation
  }
}

function request(options, callback) {
  const req = new MockClientRequest();
  
  if (callback) {
    req.on('response', callback);
  }
  
  // Return the request object immediately
  return req;
}

function get(options, callback) {
  const req = request(options, callback);
  req.end();
  return req;
}

// Mock Agent class
class Agent {
  constructor(options = {}) {
    this.options = options;
    this.requests = {};
    this.sockets = {};
    this.freeSockets = {};
    this.keepAliveMsecs = options.keepAliveMsecs || 1000;
    this.keepAlive = options.keepAlive || false;
    this.maxSockets = options.maxSockets || Infinity;
    this.maxFreeSockets = options.maxFreeSockets || 256;
  }

  addRequest(req, options) {
    // Mock implementation
  }

  createConnection(options, callback) {
    // Mock implementation
    if (callback) {
      setTimeout(() => {
        const mockSocket = new EventEmitter();
        callback(null, mockSocket);
      }, 0);
    }
  }

  keepSocketAlive(socket) {
    // Mock implementation
    return true;
  }

  reuseSocket(socket, req) {
    // Mock implementation
  }

  destroy() {
    // Mock implementation
  }
}

// Create a global agent instance
const globalAgent = new Agent();

// Export the mock https module
module.exports = {
  request,
  get,
  Agent,
  globalAgent,
  // Constants
  METHODS: ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS', 'PATCH'],
  STATUS_CODES: {
    200: 'OK',
    404: 'Not Found',
    500: 'Internal Server Error'
  }
};

// Also export as individual exports
module.exports.request = request;
module.exports.get = get;
module.exports.Agent = Agent;
module.exports.globalAgent = globalAgent;
