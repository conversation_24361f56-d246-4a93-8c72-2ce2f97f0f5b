// Mock stream implementation for React Native
// This is used to replace the 'stream' module which is not compatible with React Native

const { EventEmitter } = require('events');

class Readable extends EventEmitter {
  constructor(options = {}) {
    super();
    this.readable = true;
    this.readableEnded = false;
    this.readableFlowing = null;
    this.readableHighWaterMark = options.highWaterMark || 16384;
    this.readableBuffer = [];
    this.readableObjectMode = options.objectMode || false;
  }

  _read() {
    // Override in subclasses
  }

  read(size) {
    return null;
  }

  push(chunk) {
    if (chunk === null) {
      this.readableEnded = true;
      this.emit('end');
      return false;
    }
    this.emit('data', chunk);
    return true;
  }

  pipe(destination) {
    this.on('data', (chunk) => {
      if (destination.write) {
        destination.write(chunk);
      }
    });
    this.on('end', () => {
      if (destination.end) {
        destination.end();
      }
    });
    return destination;
  }

  unpipe() {
    // Mock implementation
  }

  pause() {
    this.readableFlowing = false;
    return this;
  }

  resume() {
    this.readableFlowing = true;
    return this;
  }
}

class Writable extends EventEmitter {
  constructor(options = {}) {
    super();
    this.writable = true;
    this.writableEnded = false;
    this.writableHighWaterMark = options.highWaterMark || 16384;
  }

  _write(chunk, encoding, callback) {
    // Override in subclasses
    if (callback) callback();
  }

  write(chunk, encoding, callback) {
    if (typeof encoding === 'function') {
      callback = encoding;
      encoding = 'utf8';
    }
    this._write(chunk, encoding, callback);
    return true;
  }

  end(chunk, encoding, callback) {
    if (chunk) {
      this.write(chunk, encoding);
    }
    this.writableEnded = true;
    this.emit('finish');
    if (callback) callback();
  }
}

class Duplex extends Readable {
  constructor(options = {}) {
    super(options);
    this.writable = true;
    this.writableEnded = false;
  }

  _write(chunk, encoding, callback) {
    if (callback) callback();
  }

  write(chunk, encoding, callback) {
    if (typeof encoding === 'function') {
      callback = encoding;
      encoding = 'utf8';
    }
    this._write(chunk, encoding, callback);
    return true;
  }

  end(chunk, encoding, callback) {
    if (chunk) {
      this.write(chunk, encoding);
    }
    this.writableEnded = true;
    this.emit('finish');
    if (callback) callback();
  }
}

class Transform extends Duplex {
  constructor(options = {}) {
    super(options);
  }

  _transform(chunk, encoding, callback) {
    // Override in subclasses
    this.push(chunk);
    if (callback) callback();
  }
}

class PassThrough extends Transform {
  constructor(options = {}) {
    super(options);
  }
}

// Export all stream classes
module.exports = {
  Readable,
  Writable,
  Duplex,
  Transform,
  PassThrough,
  Stream: Readable
};

// Also export as individual exports
module.exports.Readable = Readable;
module.exports.Writable = Writable;
module.exports.Duplex = Duplex;
module.exports.Transform = Transform;
module.exports.PassThrough = PassThrough;
