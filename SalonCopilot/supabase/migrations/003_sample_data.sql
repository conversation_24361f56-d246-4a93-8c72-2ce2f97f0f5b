-- Sample data for development (only insert if in development environment)
-- This file should be run manually in development environments only

-- Sample services
INSERT INTO services (id, professional_id, name, description, duration_minutes, price, category) VALUES
(uuid_generate_v4(), auth.uid(), 'Haircut & Style', 'Professional haircut with styling', 60, 45.00, 'Hair'),
(uuid_generate_v4(), auth.uid(), 'Color Touch-up', 'Root touch-up and color refresh', 90, 75.00, 'Color'),
(uuid_generate_v4(), auth.uid(), 'Full Color', 'Complete hair color transformation', 180, 150.00, 'Color'),
(uuid_generate_v4(), auth.uid(), 'Highlights', 'Partial or full highlights', 120, 120.00, 'Color'),
(uuid_generate_v4(), auth.uid(), 'Deep Conditioning', 'Intensive hair treatment', 45, 35.00, 'Treatment'),
(uuid_generate_v4(), auth.uid(), 'Keratin Treatment', 'Smoothing keratin treatment', 240, 300.00, 'Treatment'),
(uuid_generate_v4(), auth.uid(), 'Blowout', 'Professional styling and blowout', 45, 40.00, 'Styling'),
(uuid_generate_v4(), auth.uid(), 'Updo', 'Special occasion updo styling', 90, 80.00, 'Styling');

-- Sample availability (Monday to Friday, 9 AM to 6 PM)
INSERT INTO availability (professional_id, day_of_week, start_time, end_time) VALUES
(auth.uid(), 1, '09:00:00', '18:00:00'), -- Monday
(auth.uid(), 2, '09:00:00', '18:00:00'), -- Tuesday
(auth.uid(), 3, '09:00:00', '18:00:00'), -- Wednesday
(auth.uid(), 4, '09:00:00', '18:00:00'), -- Thursday
(auth.uid(), 5, '09:00:00', '18:00:00'), -- Friday
(auth.uid(), 6, '10:00:00', '16:00:00'); -- Saturday

-- Sample inventory items
INSERT INTO inventory (professional_id, name, brand, category, subcategory, current_stock, min_stock_level, unit_cost, selling_price) VALUES
(auth.uid(), 'Professional Shampoo', 'Redken', 'Hair Care', 'Shampoo', 5, 2, 15.00, 25.00),
(auth.uid(), 'Color Safe Conditioner', 'Redken', 'Hair Care', 'Conditioner', 4, 2, 18.00, 28.00),
(auth.uid(), 'Hair Color - Dark Brown', 'L''Oreal', 'Color', 'Permanent', 8, 3, 12.00, 20.00),
(auth.uid(), 'Hair Color - Blonde', 'L''Oreal', 'Color', 'Permanent', 6, 3, 12.00, 20.00),
(auth.uid(), 'Developer 20 Vol', 'L''Oreal', 'Color', 'Developer', 10, 4, 8.00, 15.00),
(auth.uid(), 'Bleach Powder', 'Wella', 'Color', 'Bleach', 3, 2, 25.00, 40.00),
(auth.uid(), 'Hair Mask Treatment', 'Olaplex', 'Treatment', 'Mask', 7, 3, 30.00, 50.00),
(auth.uid(), 'Heat Protectant Spray', 'Redken', 'Styling', 'Protection', 12, 5, 10.00, 18.00),
(auth.uid(), 'Volumizing Mousse', 'Redken', 'Styling', 'Volume', 8, 3, 12.00, 22.00),
(auth.uid(), 'Finishing Spray', 'L''Oreal', 'Styling', 'Finish', 6, 2, 8.00, 15.00);

-- Function to create sample clients (to be called after authentication)
CREATE OR REPLACE FUNCTION create_sample_clients()
RETURNS VOID AS $$
BEGIN
    -- Only create if no clients exist for this user
    IF NOT EXISTS (SELECT 1 FROM clients WHERE professional_id = auth.uid()) THEN
        INSERT INTO clients (professional_id, first_name, last_name, phone, email, hair_type, hair_texture, preferences) VALUES
        (auth.uid(), 'Maria', 'Garcia', '******-0101', '<EMAIL>', 'Curly', 'Coarse', '{"preferred_time": "morning", "allergies": []}'),
        (auth.uid(), 'Jennifer', 'Smith', '******-0102', '<EMAIL>', 'Straight', 'Fine', '{"preferred_time": "afternoon", "allergies": ["ammonia"]}'),
        (auth.uid(), 'Sarah', 'Johnson', '******-0103', '<EMAIL>', 'Wavy', 'Medium', '{"preferred_time": "evening", "allergies": []}'),
        (auth.uid(), 'Lisa', 'Brown', '******-0104', '<EMAIL>', 'Straight', 'Thick', '{"preferred_time": "morning", "allergies": ["parabens"]}'),
        (auth.uid(), 'Amanda', 'Davis', '******-0105', '<EMAIL>', 'Curly', 'Fine', '{"preferred_time": "afternoon", "allergies": []}');
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create sample appointments (to be called after clients are created)
CREATE OR REPLACE FUNCTION create_sample_appointments()
RETURNS VOID AS $$
DECLARE
    client_ids UUID[];
    service_ids UUID[];
    appointment_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get client and service IDs for this professional
    SELECT ARRAY(SELECT id FROM clients WHERE professional_id = auth.uid() LIMIT 5) INTO client_ids;
    SELECT ARRAY(SELECT id FROM services WHERE professional_id = auth.uid() LIMIT 8) INTO service_ids;
    
    -- Only create if we have clients and services
    IF array_length(client_ids, 1) > 0 AND array_length(service_ids, 1) > 0 THEN
        -- Create appointments for the next few days
        FOR i IN 1..10 LOOP
            appointment_date := NOW() + (i || ' days')::INTERVAL + '10:00:00'::TIME;
            
            INSERT INTO appointments (
                professional_id, 
                client_id, 
                service_id, 
                scheduled_date, 
                duration_minutes, 
                status,
                price
            ) VALUES (
                auth.uid(),
                client_ids[1 + (i % array_length(client_ids, 1))],
                service_ids[1 + (i % array_length(service_ids, 1))],
                appointment_date,
                CASE 
                    WHEN i % 4 = 0 THEN 180  -- Long appointment
                    WHEN i % 3 = 0 THEN 120  -- Medium appointment
                    ELSE 60                   -- Short appointment
                END,
                CASE 
                    WHEN i <= 3 THEN 'completed'
                    WHEN i <= 6 THEN 'scheduled'
                    ELSE 'confirmed'
                END,
                CASE 
                    WHEN i % 4 = 0 THEN 150.00
                    WHEN i % 3 = 0 THEN 120.00
                    ELSE 45.00
                END
            );
        END LOOP;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to initialize sample data for a new user
CREATE OR REPLACE FUNCTION initialize_sample_data()
RETURNS VOID AS $$
BEGIN
    PERFORM create_sample_clients();
    PERFORM create_sample_appointments();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
