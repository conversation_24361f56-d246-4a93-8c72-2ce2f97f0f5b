-- Enable Row Level Security on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE time_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE formulations ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS user_role AS $$
BEGIN
    RETURN (
        SELECT role 
        FROM profiles 
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Profiles policies
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Clients policies
CREATE POLICY "Professionals can view their own clients" ON clients
    FOR SELECT USING (professional_id = auth.uid());

CREATE POLICY "Professionals can insert clients" ON clients
    FOR INSERT WITH CHECK (professional_id = auth.uid());

CREATE POLICY "Professionals can update their own clients" ON clients
    FOR UPDATE USING (professional_id = auth.uid());

CREATE POLICY "Professionals can delete their own clients" ON clients
    FOR DELETE USING (professional_id = auth.uid());

-- Services policies
CREATE POLICY "Professionals can view their own services" ON services
    FOR SELECT USING (professional_id = auth.uid());

CREATE POLICY "Professionals can insert services" ON services
    FOR INSERT WITH CHECK (professional_id = auth.uid());

CREATE POLICY "Professionals can update their own services" ON services
    FOR UPDATE USING (professional_id = auth.uid());

CREATE POLICY "Professionals can delete their own services" ON services
    FOR DELETE USING (professional_id = auth.uid());

-- Availability policies
CREATE POLICY "Professionals can view their own availability" ON availability
    FOR SELECT USING (professional_id = auth.uid());

CREATE POLICY "Professionals can insert availability" ON availability
    FOR INSERT WITH CHECK (professional_id = auth.uid());

CREATE POLICY "Professionals can update their own availability" ON availability
    FOR UPDATE USING (professional_id = auth.uid());

CREATE POLICY "Professionals can delete their own availability" ON availability
    FOR DELETE USING (professional_id = auth.uid());

-- Time blocks policies
CREATE POLICY "Professionals can view their own time blocks" ON time_blocks
    FOR SELECT USING (professional_id = auth.uid());

CREATE POLICY "Professionals can insert time blocks" ON time_blocks
    FOR INSERT WITH CHECK (professional_id = auth.uid());

CREATE POLICY "Professionals can update their own time blocks" ON time_blocks
    FOR UPDATE USING (professional_id = auth.uid());

CREATE POLICY "Professionals can delete their own time blocks" ON time_blocks
    FOR DELETE USING (professional_id = auth.uid());

-- Appointments policies
CREATE POLICY "Professionals can view their own appointments" ON appointments
    FOR SELECT USING (professional_id = auth.uid());

CREATE POLICY "Professionals can insert appointments" ON appointments
    FOR INSERT WITH CHECK (professional_id = auth.uid());

CREATE POLICY "Professionals can update their own appointments" ON appointments
    FOR UPDATE USING (professional_id = auth.uid());

CREATE POLICY "Professionals can delete their own appointments" ON appointments
    FOR DELETE USING (professional_id = auth.uid());

-- Inventory policies
CREATE POLICY "Professionals can view their own inventory" ON inventory
    FOR SELECT USING (professional_id = auth.uid());

CREATE POLICY "Professionals can insert inventory" ON inventory
    FOR INSERT WITH CHECK (professional_id = auth.uid());

CREATE POLICY "Professionals can update their own inventory" ON inventory
    FOR UPDATE USING (professional_id = auth.uid());

CREATE POLICY "Professionals can delete their own inventory" ON inventory
    FOR DELETE USING (professional_id = auth.uid());

-- Formulations policies
CREATE POLICY "Professionals can view their own formulations" ON formulations
    FOR SELECT USING (professional_id = auth.uid());

CREATE POLICY "Professionals can insert formulations" ON formulations
    FOR INSERT WITH CHECK (professional_id = auth.uid());

CREATE POLICY "Professionals can update their own formulations" ON formulations
    FOR UPDATE USING (professional_id = auth.uid());

CREATE POLICY "Professionals can delete their own formulations" ON formulations
    FOR DELETE USING (professional_id = auth.uid());

-- Additional policies for admin users (if needed)
CREATE POLICY "Admins can view all profiles" ON profiles
    FOR SELECT USING (get_user_role() = 'admin');

CREATE POLICY "Admins can update all profiles" ON profiles
    FOR UPDATE USING (get_user_role() = 'admin');

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, role)
    VALUES (NEW.id, NEW.email, 'professional');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile when user signs up
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to check appointment conflicts
CREATE OR REPLACE FUNCTION check_appointment_conflict(
    p_professional_id UUID,
    p_start_time TIMESTAMP WITH TIME ZONE,
    p_duration_minutes INTEGER,
    p_appointment_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    p_end_time TIMESTAMP WITH TIME ZONE;
    conflict_count INTEGER;
BEGIN
    p_end_time := p_start_time + (p_duration_minutes || ' minutes')::INTERVAL;
    
    SELECT COUNT(*)
    INTO conflict_count
    FROM appointments
    WHERE professional_id = p_professional_id
        AND status NOT IN ('cancelled', 'no_show')
        AND (p_appointment_id IS NULL OR id != p_appointment_id)
        AND (
            (scheduled_date <= p_start_time AND scheduled_date + (duration_minutes || ' minutes')::INTERVAL > p_start_time)
            OR
            (scheduled_date < p_end_time AND scheduled_date + (duration_minutes || ' minutes')::INTERVAL >= p_end_time)
            OR
            (scheduled_date >= p_start_time AND scheduled_date + (duration_minutes || ' minutes')::INTERVAL <= p_end_time)
        );
    
    RETURN conflict_count = 0;
END;
$$ LANGUAGE plpgsql;

-- Function to get available time slots
CREATE OR REPLACE FUNCTION get_available_slots(
    p_professional_id UUID,
    p_date DATE,
    p_duration_minutes INTEGER DEFAULT 60
)
RETURNS TABLE(start_time TIMESTAMP WITH TIME ZONE, end_time TIMESTAMP WITH TIME ZONE) AS $$
DECLARE
    slot_start TIMESTAMP WITH TIME ZONE;
    slot_end TIMESTAMP WITH TIME ZONE;
    day_start TIME;
    day_end TIME;
    current_slot TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get availability for the day of week
    SELECT a.start_time, a.end_time
    INTO day_start, day_end
    FROM availability a
    WHERE a.professional_id = p_professional_id
        AND a.day_of_week = EXTRACT(DOW FROM p_date)
        AND a.is_active = true
    LIMIT 1;
    
    IF day_start IS NULL THEN
        RETURN; -- No availability for this day
    END IF;
    
    slot_start := p_date + day_start;
    slot_end := p_date + day_end;
    current_slot := slot_start;
    
    -- Generate 30-minute slots and check availability
    WHILE current_slot + (p_duration_minutes || ' minutes')::INTERVAL <= slot_end LOOP
        IF check_appointment_conflict(p_professional_id, current_slot, p_duration_minutes) THEN
            start_time := current_slot;
            end_time := current_slot + (p_duration_minutes || ' minutes')::INTERVAL;
            RETURN NEXT;
        END IF;
        current_slot := current_slot + '30 minutes'::INTERVAL;
    END LOOP;
    
    RETURN;
END;
$$ LANGUAGE plpgsql;
