# Overview  
Salon Copilot (Replit Edition) es una aplicación web y móvil multiplataforma diseñada como "copiloto experto" digital para profesionales de coloración capilar. La aplicación optimiza la gestión de información, mejora la precisión del diagnóstico capilar y facilita la creación de formulaciones de color personalizadas utilizando inteligencia artificial que se adapta al estilo único de cada profesional.

**Problema que resuelve:**
- Falta de herramientas especializadas en coloración capilar con IA
- Gestión ineficiente de inventario y formulaciones
- Brecha entre profesionales novatos y expertos
- Ausencia de sistemas que evolucionen con el estilo personal del profesional

**Usuarios objetivo:**
- Profesionales independientes (freelance, chair rental, domicilio)
- Estilistas de salón (tradicionales, boutique, cadenas)
- Coordinadores y administradores de salón
- Profesionales en formación

**Propuesta de valor diferencial:**
- Configuración Invisible: Se configura automáticamente mientras trabajas (30 segundos)
- Inventario Adaptativo: Desde gestión básica hasta control detallado
- IA Evolutiva: Aprende el estilo personal y mejora con cada servicio
- Lenguaje Inclusivo: Diseñada para toda la diversidad del sector
- Inteligencia Contextual: Predicciones y sugerencias proactivas

# Core Features  

## 1. Sistema de Configuración Inteligente
**Qué hace:** Configuración inicial ultra-simplificada en 30 segundos que evoluciona automáticamente
**Por qué es importante:** Elimina barreras de adopción y se adapta al flujo de trabajo natural
**Cómo funciona:** 
- 3 pantallas de configuración inicial (identidad profesional, contexto de trabajo, nivel de inventario)
- Aprendizaje automático silencioso durante las primeras semanas
- Personalización progresiva basada en patrones detectados

## 2. Sistema de Inventario Adaptativo
**Qué hace:** Tres modalidades de gestión según las necesidades del usuario
**Por qué es importante:** Se adapta desde principiantes hasta gestión empresarial avanzada
**Cómo funciona:**
- Modo 1: Solo recomendaciones (sin inventario detallado)
- Modo 2: Control básico (disponibilidad sí/no)
- Modo 3: Control detallado (cantidades exactas, alertas predictivas)

## 3. Diagnóstico Inteligente con IA
**Qué hace:** Análisis capilar automatizado usando GPT-4o con validación profesional
**Por qué es importante:** Mejora precisión y reduce tiempo de diagnóstico
**Cómo funciona:**
- Captura de 3-5 fotos optimizadas
- Análisis automático en 15-30 segundos
- Detección de tratamientos previos, daños y porosidad
- Modalidades: Asistente IA, Manual, o Híbrido

## 4. Formulación Personalizada
**Qué hace:** Genera formulaciones precisas adaptadas al inventario disponible
**Por qué es importante:** Optimiza recursos y mejora consistencia de resultados
**Cómo funciona:**
- Formulaciones basadas en diagnóstico IA
- Adaptación automática según inventario disponible
- Cálculo de rentabilidad por servicio
- Sugerencias de alternativas y optimizaciones

## 5. Gestión de Citas y Calendario Inteligente
**Qué hace:** Sistema completo de programación con IA predictiva
**Por qué es importante:** Optimiza tiempo y mejora experiencia del cliente
**Cómo funciona:**
- Calendario inteligente con disponibilidad automática
- Recordatorios multicapa (SMS, email, push)
- Programación automática de seguimientos
- Integración con flujo de servicio

## 6. Sistema Multi-Usuario y Colaboración
**Qué hace:** Arquitectura flexible para profesionales independientes y salones
**Por qué es importante:** Escala desde uso individual hasta equipos grandes
**Cómo funciona:**
- Roles flexibles (Independiente, Profesional de Salón, Coordinador)
- Inventario compartido opcional
- Privacidad granular de datos
- Colaboración inteligente entre profesionales

## 7. Auto-completado Inteligente con Perplexity
**Qué hace:** Completa automáticamente información de productos
**Por qué es importante:** Reduce tiempo de setup y mantiene base de datos actualizada
**Cómo funciona:**
- Búsqueda en tiempo real de especificaciones
- Reconocimiento por imagen y códigos de barras
- Actualización automática de precios y disponibilidad
- Sugerencias de productos equivalentes

## 8. Documentación y Evolución Continua
**Qué hace:** Captura resultados y aprende para mejorar futuras recomendaciones
**Por qué es importante:** Crea un ciclo de mejora continua personalizado
**Cómo funciona:**
- Comparación automática antes/después
- Análisis de precisión vs objetivo
- Aprendizaje de patrones exitosos
- Programación automática de seguimientos

# User Experience  

## Perfiles de Usuario

### Profesional Independiente
- **Características:** Gestión autónoma, flexibilidad total, construcción de marca personal
- **Necesidades:** Herramientas profesionales accesibles, optimización de rentabilidad
- **Flujo típico:** Configuración personal → Gestión de clientes → Servicios individualizados

### Profesional de Salón
- **Características:** Trabajo en equipo, protocolos establecidos, inventario compartido
- **Necesidades:** Coordinación, consistencia, gestión de clientela cruzada
- **Flujo típico:** Configuración de salón → Colaboración → Servicios coordinados

### Coordinador de Salón
- **Características:** Gestión de equipo, supervisión, reportes
- **Necesidades:** Vista consolidada, control de inventario, métricas de productividad
- **Flujo típico:** Setup de salón → Gestión de equipo → Análisis y reportes

## Flujos de Usuario Clave

### Flujo de Configuración Inicial (30 segundos)
1. Identidad profesional (especialidad, estilo)
2. Contexto de trabajo (independiente/salón)
3. Nivel de inventario deseado
4. Configuración automática completada

### Flujo Principal de Servicio (4 pasos)
1. **Diagnóstico:** Captura de fotos → Análisis IA → Validación profesional
2. **Objetivo:** Definición de resultado deseado → Análisis de viabilidad
3. **Formulación:** Generación automática → Adaptación a inventario → Cálculo de costos
4. **Resultado:** Documentación → Aprendizaje del sistema → Programación de seguimiento

### Flujo de Gestión de Citas
1. **Programación:** Selección de cliente → Tipo de servicio → Fecha/hora disponible
2. **Confirmación:** Recordatorios automáticos → Confirmación del cliente
3. **Servicio:** Ejecución del flujo principal → Documentación
4. **Seguimiento:** Programación automática → Evaluación de satisfacción

## Consideraciones UI/UX

### Diseño Universal
- Accesibilidad total: contraste alto, texto escalable, navegación por voz
- Lenguaje neutro: terminología profesional sin sesgos
- Iconografía inclusiva: símbolos universales, diversidad representativa

### Interfaces Adaptativas
- **Vista Compacta:** Para profesionales experimentados (información densa)
- **Vista Guiada:** Para profesionales en desarrollo (paso a paso)
- **Modo Salón:** Optimizado para uso colaborativo

### Usabilidad Específica
- Uso con guantes: botones grandes, gestos simplificados
- Iluminación de salón: alto contraste, modo día/noche
- Multitarea: acceso rápido, navegación por pestañas

# Technical Architecture  

## Stack Tecnológico

### Frontend Universal
- **React Native + Expo SDK 52:** Desarrollo multiplataforma nativo
- **Tailwind CSS:** Diseño responsive y consistente
- **Expo EAS Build:** Deployment nativo iOS/Android
- **Progressive Web App (PWA):** Acceso web sin instalación

### Backend Robusto
- **Supabase:** Backend principal (PostgreSQL + Real-time + Storage)
- **Row Level Security (RLS):** Aislamiento seguro de datos por usuario
- **Supabase Auth:** Autenticación social y magic links
- **Edge Functions:** Procesamiento serverless para tareas pesadas

### Inteligencia Artificial
- **GPT-4o:** Análisis de imágenes capilares y formulación inteligente
- **Perplexity API:** Auto-completado de información de productos
- **Machine Learning local:** Análisis básico offline
- **Aprendizaje progresivo:** Personalización de preferencias

### Almacenamiento y CDN
- **Supabase Storage:** Imágenes con CDN global
- **Compresión inteligente:** Optimización automática de imágenes
- **Backup automático:** Cada 4 horas
- **Sincronización offline-online:** Seamless

## Componentes del Sistema

### Base de Datos (Supabase PostgreSQL)
```sql
-- Tablas principales
- profiles: Usuarios y configuración profesional
- clients: Gestión de clientes
- appointments: Sistema de citas y calendario
- availability: Disponibilidad del profesional
- time_blocks: Bloqueos temporales (vacaciones, etc.)
- services: Historial de servicios realizados
- inventory: Gestión de productos
- formulations: Formulaciones y resultados
```

### APIs y Integraciones
- **GPT-4o API:** Análisis visual y generación de formulaciones
- **Perplexity API:** Búsqueda y auto-completado de productos
- **Supabase Real-time:** Sincronización en tiempo real
- **Expo Notifications:** Sistema de recordatorios push
- **Expo Calendar:** Integración con calendarios nativos

### Arquitectura de Seguridad
- **Row Level Security:** Aislamiento total de datos por usuario
- **Encriptación end-to-end:** Para imágenes de clientes
- **Auditoría completa:** Logs de accesos y modificaciones
- **Backup diferencial:** Por usuario/organización

## Infraestructura

### Desarrollo y Deployment
- **Replit:** Plataforma de desarrollo colaborativo
- **Expo EAS:** Build y deployment automático
- **Supabase Edge:** CDN global para assets
- **GitHub:** Control de versiones y CI/CD

### Escalabilidad
- **MVP:** Plan gratuito Replit + Supabase
- **Growth:** Upgrade automático según métricas
- **Enterprise:** Migración a infraestructura dedicada

# Development Roadmap  

## Fase 1: MVP Funcional
**Objetivo:** Sistema básico funcional con valor inmediato

### Setup y Arquitectura Base
- Configuración inicial en Replit
- Setup de Supabase (database, auth, storage)
- Estructura base de React Native + Expo
- Implementación de autenticación básica
- Schema de base de datos inicial

### Sistema de Configuración Inteligente
- Onboarding de 30 segundos (3 pantallas)
- Configuración de perfil profesional básica
- Integración inicial con GPT-4o
- Base de datos de productos inicial
- Sistema de preferencias básico

### Gestión de Clientes y Citas
- CRUD completo de clientes
- Calendario inteligente con disponibilidad
- Sistema de citas básico
- Recordatorios automáticos (email/SMS)
- Integración con calendarios nativos

### Flujo Principal de Servicio
- Diagnóstico con IA (GPT-4o)
- Captura y análisis de imágenes
- Sistema de formulación básico
- Documentación de resultados
- Historial de servicios

### Sistema de Inventario Adaptativo
- Tres modos de inventario implementados
- Integración con Perplexity para auto-completado
- Sistema de alertas y sugerencias básicas
- Gestión de productos con cantidades

## Fase 2: Optimización y Mobile
**Objetivo:** Aplicaciones nativas optimizadas con IA avanzada

### Aplicaciones Nativas
- Build de aplicaciones iOS y Android
- Optimización para tiendas de aplicaciones
- Testing en dispositivos reales
- Implementación de notificaciones push
- Funcionalidades offline básicas

### IA Avanzada y Personalización
- Sistema de aprendizaje automático personal
- Predicciones contextuales avanzadas
- Optimización basada en patrones de uso
- Análisis de precisión y mejora continua
- Formulaciones predictivas

### Multi-Usuario y Colaboración
- Sistema de roles y permisos
- Funcionalidades de salón (inventario compartido)
- Dashboard de administración
- Comunicación entre profesionales
- Gestión de equipos

### Optimización de Performance
- Caching inteligente
- Optimización de imágenes
- Reducción de tiempo de carga
- Mejora de UX/UI basada en feedback
- Testing exhaustivo

## Fase 3: Escalado y Funcionalidades Avanzadas
**Objetivo:** Plataforma completa lista para escalar

### Funcionalidades Avanzadas
- Reportes y analytics avanzados
- Integraciones con sistemas de pago
- API para terceros
- Sistema de respaldo y recuperación
- Funcionalidades de exportación

### Comunidad y Marketplace
- Red social de profesionales
- Marketplace de técnicas y formulaciones
- Sistema de reviews y ratings
- Contenido educativo integrado
- Certificaciones digitales

### Escalabilidad Empresarial
- Multi-tenancy avanzado
- White-label solutions
- Integraciones enterprise
- SLA y soporte dedicado
- Compliance y certificaciones

### Innovación Continua
- Realidad aumentada para preview
- Análisis espectrofotométrico
- IoT integration
- Blockchain para certificaciones
- AI generativa para nuevas técnicas

# Logical Dependency Chain

## Fundación (Debe construirse primero)
1. **Setup técnico base:** Replit + Supabase + React Native
2. **Autenticación y seguridad:** Auth + RLS + permisos básicos
3. **Schema de base de datos:** Estructura completa con relaciones
4. **Componentes UI básicos:** Design system y componentes reutilizables

## Funcionalidades Core (Orden lógico)
5. **Sistema de configuración:** Onboarding y perfiles de usuario
6. **Gestión de clientes:** CRUD básico antes de servicios
7. **Sistema de citas:** Calendario y programación antes de servicios
8. **Integración IA básica:** GPT-4o para diagnóstico
9. **Flujo de servicio MVP:** Diagnóstico → Formulación → Documentación
10. **Inventario básico:** Gestión de productos antes de formulaciones avanzadas

## Optimización y Escalado (Construir sobre base sólida)
11. **Inventario adaptativo:** Tres modalidades sobre base de inventario
12. **IA avanzada:** Personalización y aprendizaje automático
13. **Multi-usuario:** Roles y colaboración sobre base de usuarios
14. **Aplicaciones nativas:** Build sobre PWA funcional
15. **Analytics y reportes:** Sobre datos existentes de servicios

## Principios de Desarrollo
- **Iterativo:** Cada feature debe ser usable inmediatamente
- **Incremental:** Construir sobre funcionalidades existentes
- **Testeable:** Cada componente debe ser verificable independientemente
- **Escalable:** Arquitectura que soporte crecimiento desde día 1

# Risks and Mitigations  

## Riesgos Técnicos

### Alto: Dependencia de APIs Externas
**Riesgo:** Caída de GPT-4o o Perplexity afecta funcionalidad core
**Mitigación:**
- Implementación de modelos de IA locales como fallback
- Cache inteligente de respuestas frecuentes
- Degradación elegante a modo manual temporal
- SLA con proveedores y monitoring proactivo

### Medio: Escalabilidad de Replit
**Riesgo:** Limitaciones de plataforma en crecimiento rápido
**Mitigación:**
- Arquitectura híbrida Replit + Supabase desde MVP
- Plan de migración a infraestructura dedicada preparado
- Load testing regular con simulación de carga
- Partnership técnico con Replit para soporte enterprise

### Medio: Precisión de IA en Casos Edge
**Riesgo:** IA falla en casos complejos, daña confianza
**Mitigación:**
- Sistema de validación obligatoria por profesional
- Marcado de casos complejos para review manual
- Feedback loop para mejora continua del modelo
- Transparencia sobre limitaciones de la IA

## Riesgos de Producto

### Alto: Adopción Lenta del Sector
**Riesgo:** Resistencia al cambio en sector tradicionalmente conservador
**Mitigación:**
- Programa de beta testing extensivo con líderes de opinión
- Contenido educativo sobre beneficios concretos
- Estrategia de influencer marketing con profesionales reconocidos
- Plan freemium para reducir barreras de entrada

### Medio: Complejidad de Configuración
**Riesgo:** Sistema demasiado complejo para adopción masiva
**Mitigación:**
- Configuración de 30 segundos como objetivo no negociable
- Testing de usabilidad continuo con usuarios reales
- Configuración automática inteligente
- Múltiples niveles de complejidad según experiencia

## Riesgos de Negocio

### Medio: Entrada de Competidores con Más Recursos
**Riesgo:** Gigantes del software lanzan productos similares
**Mitigación:**
- Focus en nicho específico (coloración) vs generalista
- Construcción de comunidad y network effects
- Innovación continua en IA especializada
- Partnerships estratégicos con marcas de productos

### Bajo: Regulaciones de Privacidad
**Riesgo:** Cambios en regulaciones afectan manejo de datos
**Mitigación:**
- Cumplimiento GDPR desde día 1
- Arquitectura privacy-by-design
- Auditorías regulares de seguridad
- Flexibilidad en configuración de privacidad

## MVP Crítico para Validación
**Elementos mínimos para probar viabilidad:**
1. Configuración en 30 segundos funcional
2. Diagnóstico IA con 80%+ precisión
3. Formulación básica que genere valor inmediato
4. Sistema de citas que ahorre tiempo real
5. Inventario básico que optimice recursos

# Appendix  

## Especificaciones Técnicas Detalladas

### Configuración de Expo
```javascript
// app.config.js
export default {
  expo: {
    name: "Salon Copilot",
    slug: "salon-copilot",
    version: "1.0.0",
    platforms: ["ios", "android", "web"],
    plugins: [
      "expo-camera",
      "expo-image-picker", 
      "expo-notifications",
      "expo-calendar"
    ]
  }
};
```

### Schema de Base de Datos Crítico
```sql
-- Usuarios y configuración
CREATE TABLE profiles (
    id UUID REFERENCES auth.users PRIMARY KEY,
    professional_type VARCHAR(50),
    salon_name VARCHAR(255),
    experience_level VARCHAR(20),
    preferred_brands TEXT[],
    inventory_mode VARCHAR(20),
    ai_learning_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Citas y calendario
CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    professional_id UUID REFERENCES profiles(id),
    client_id UUID REFERENCES clients(id),
    service_type VARCHAR(100),
    scheduled_date TIMESTAMP NOT NULL,
    duration_minutes INTEGER DEFAULT 120,
    status VARCHAR(20) DEFAULT 'scheduled',
    reminder_sent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Servicios realizados
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    appointment_id UUID REFERENCES appointments(id),
    client_id UUID REFERENCES clients(id),
    professional_id UUID REFERENCES profiles(id),
    diagnosis JSONB,
    target_color JSONB,
    formulation JSONB,
    result JSONB,
    images TEXT[],
    ai_accuracy_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Integración con APIs de IA
```javascript
// Configuración GPT-4o
const openaiConfig = {
  model: "gpt-4o",
  max_tokens: 4000,
  temperature: 0.2,
  response_format: { type: "json_object" }
};

// Configuración Perplexity
const perplexityConfig = {
  model: "sonar-medium-online",
  max_tokens: 1000,
  temperature: 0.1
};
```

## Métricas de Éxito Clave

### Métricas de Adopción
- Tiempo de configuración promedio: < 45 segundos
- Tasa de conversión onboarding: > 80%
- Retención día 7: > 60%
- Retención día 30: > 40%

### Métricas de Producto
- Precisión de IA por diagnóstico: > 85%
- Reducción de tiempo por servicio: 20-30%
- Satisfacción del usuario (NPS): > 50
- Servicios documentados por usuario/mes: > 15

### Métricas de Negocio
- CAC (Customer Acquisition Cost): < €25
- LTV (Lifetime Value): > €200
- Churn rate mensual: < 8%
- Conversión freemium a pago: > 15%

## Consideraciones de Localización
- Soporte inicial: Español, Inglés
- Monedas: EUR, USD, GBP
- Regulaciones: GDPR (Europa), CCPA (California)
- Marcas de productos: Base europea inicial, expansión global

## Plan de Testing
- **Unit Testing:** Jest + React Native Testing Library
- **Integration Testing:** Detox para E2E móvil
- **API Testing:** Supabase + PostgREST
- **AI Testing:** Datasets de imágenes capilares validadas
- **Performance Testing:** Maestro para mobile performance
